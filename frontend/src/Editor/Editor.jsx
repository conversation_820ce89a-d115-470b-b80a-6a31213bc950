import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiSave,
  FiPlay,
  FiTerminal,
  FiMessageSquare,
  FiX,
  FiMenu,
  FiMoon,
  FiSun,
  FiFolder,
  FiFile,
  FiChevronRight,
  FiChevronDown
} from "react-icons/fi";
import Editor from "@monaco-editor/react";
import { Terminal } from "@xterm/xterm";
import { FitAddon } from "@xterm/addon-fit";
import "@xterm/xterm/css/xterm.css";
import { useAuth } from "../contexts/AuthContext";
import { containerAPI, TerminalWebSocket } from "../services/api";

export default function EditorPage() {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("editor");
  const [darkMode, setDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedMode = localStorage.getItem('darkMode');
      return savedMode ? JSON.parse(savedMode) :
        window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return true;
  });
  const [containerStatus, setContainerStatus] = useState(null);
  const [terminalOutput, setTerminalOutput] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [code, setCode] = useState(`// Welcome to CodeForge Editor
function greet(name) {
  console.log(\`Hello, \${name}! Welcome to CodeForge.\`);
}

// Try running this code
greet("Developer");

// You can write JavaScript, Python, HTML, CSS and more!
const features = [
  "Syntax highlighting",
  "Auto-completion",
  "Integrated terminal",
  "File explorer",
  "Dark/Light themes"
];

console.log("CodeForge Features:", features);`);
  const [language, setLanguage] = useState("javascript");
  const [fileName, setFileName] = useState("main.js");
  const [isFileExplorerOpen, setIsFileExplorerOpen] = useState(true);
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const wsRef = useRef(null);

  // Check authentication on component mount
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // Check container status
    checkContainerStatus();
  }, [isAuthenticated, navigate]);

  const checkContainerStatus = async () => {
    const result = await containerAPI.getStatus();
    if (result.success) {
      setContainerStatus(result.data);
    } else {
      console.error('Failed to get container status:', result.error);
    }
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const handleExit = () => {
    if (window.confirm("Are you sure you want to exit the editor? Any unsaved changes will be lost.")) {
      // Disconnect WebSocket before leaving
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
      navigate('/');
    }
  };

  const handleSave = () => {
    // TODO: Implement actual file saving to backend
    console.log("Saving file:", fileName);
    alert("File saved successfully!");
  };

  const handleRun = async () => {
    if (activeTab !== "terminal") {
      setActiveTab("terminal");
    }

    // Execute code in backend container
    if (language === "javascript") {
      const command = `node -e "${code.replace(/"/g, '\\"')}"`;
      const result = await containerAPI.executeCommand(command);

      if (result.success && xtermRef.current) {
        xtermRef.current.write(`\r\n$ ${command}\r\n`);
        xtermRef.current.write(result.data.output);
        xtermRef.current.write("$ ");
      }
    } else {
      // For other languages, just show in terminal
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n$ Running ${fileName}...\r\n`);
        xtermRef.current.write("Code execution not implemented for this language yet.\r\n");
        xtermRef.current.write("$ ");
      }
    }
  };

  // Initialize terminal when terminal tab is active
  useEffect(() => {
    if (activeTab === "terminal" && terminalRef.current && !xtermRef.current && isAuthenticated) {
      setIsConnecting(true);

      // Initialize xterm.js terminal
      xtermRef.current = new Terminal({
        theme: {
          background: darkMode ? '#1a1a1a' : '#ffffff',
          foreground: darkMode ? '#ffffff' : '#000000',
          cursor: '#00ff00',
          selection: 'rgba(255, 255, 255, 0.3)',
        },
        fontSize: 14,
        fontFamily: 'JetBrains Mono, monospace',
        cursorBlink: true,
      });

      fitAddonRef.current = new FitAddon();
      xtermRef.current.loadAddon(fitAddonRef.current);
      xtermRef.current.open(terminalRef.current);
      fitAddonRef.current.fit();

      // Initialize WebSocket connection
      wsRef.current = new TerminalWebSocket(
        (output) => {
          if (xtermRef.current) {
            xtermRef.current.write(output);
          }
          setTerminalOutput(prev => prev + output);
        },
        (error) => {
          console.error('Terminal WebSocket error:', error);
          if (xtermRef.current) {
            xtermRef.current.write(`\r\nWebSocket Error: ${error}\r\n`);
          }
          setIsConnecting(false);
        },
        () => {
          console.log('Terminal WebSocket disconnected');
          setIsConnecting(false);
        }
      );

      // Connect to backend terminal
      wsRef.current.connect();

      // Handle terminal input
      xtermRef.current.onData((data) => {
        if (wsRef.current) {
          wsRef.current.sendCommand(data);
        }
      });

      setIsConnecting(false);
    }

    return () => {
      if (xtermRef.current) {
        xtermRef.current.dispose();
        xtermRef.current = null;
        fitAddonRef.current = null;
      }
      if (wsRef.current) {
        wsRef.current.disconnect();
        wsRef.current = null;
      }
    };
  }, [activeTab, darkMode, isAuthenticated]);

  // Handle window resize for terminal
  useEffect(() => {
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        fitAddonRef.current.fit();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // File explorer data
  const fileStructure = [
    {
      name: "src",
      type: "folder",
      expanded: true,
      children: [
        { name: "main.js", type: "file", language: "javascript" },
        { name: "style.css", type: "file", language: "css" },
        { name: "index.html", type: "file", language: "html" },
      ]
    },
    {
      name: "public",
      type: "folder",
      expanded: false,
      children: [
        { name: "favicon.ico", type: "file" },
        { name: "manifest.json", type: "file", language: "json" },
      ]
    },
    { name: "package.json", type: "file", language: "json" },
    { name: "README.md", type: "file", language: "markdown" },
  ];

  const handleFileSelect = (file) => {
    if (file.type === "file") {
      setFileName(file.name);
      if (file.language) {
        setLanguage(file.language);
      }
      // In a real app, you would load the file content here
    }
  };

  const renderFileTree = (items, depth = 0) => {
    return items.map((item, index) => (
      <div key={index} style={{ paddingLeft: `${depth * 16}px` }}>
        <div
          className="flex items-center py-1 px-2 hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer text-sm"
          onClick={() => handleFileSelect(item)}
        >
          {item.type === "folder" ? (
            <>
              {item.expanded ? <FiChevronDown className="mr-1" /> : <FiChevronRight className="mr-1" />}
              <FiFolder className="mr-2 text-blue-500" />
            </>
          ) : (
            <>
              <span className="w-4 mr-1"></span>
              <FiFile className="mr-2 text-gray-500" />
            </>
          )}
          <span className={fileName === item.name ? "text-blue-600 dark:text-blue-400 font-semibold" : ""}>
            {item.name}
          </span>
        </div>
        {item.type === "folder" && item.expanded && item.children && (
          <div>
            {renderFileTree(item.children, depth + 1)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white font-mono flex flex-col">
      {/* Top Navigation Bar */}
      <nav className="bg-gray-800 dark:bg-gray-900 text-white px-4 py-2 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="p-2 hover:bg-gray-700 rounded"
          >
            <FiMenu />
          </button>
          <h1 className="text-lg font-bold">CodeForge Editor</h1>
        </div>

        <div className="flex items-center space-x-2">
          {/* User Info */}
          {user && (
            <span className="text-sm text-gray-300 mr-2">
              Welcome, {user.username}
            </span>
          )}

          {/* Container Status */}
          {containerStatus && (
            <div className={`px-2 py-1 rounded text-xs ${
              containerStatus.status === 'running'
                ? 'bg-green-600 text-white'
                : 'bg-yellow-600 text-black'
            }`}>
              Container: {containerStatus.status}
            </div>
          )}

          <button
            onClick={handleSave}
            className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm"
          >
            <FiSave />
            <span>Save</span>
          </button>
          <button
            onClick={handleRun}
            className="flex items-center space-x-1 px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-sm"
          >
            <FiPlay />
            <span>Run</span>
          </button>
          <button
            onClick={() => setActiveTab(activeTab === "terminal" ? "editor" : "terminal")}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
              activeTab === "terminal" ? "bg-yellow-600" : "bg-gray-600 hover:bg-gray-700"
            }`}
          >
            <FiTerminal />
            <span>{isConnecting ? 'Connecting...' : 'Terminal'}</span>
          </button>
          <button
            onClick={() => setActiveTab(activeTab === "chat" ? "editor" : "chat")}
            className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
              activeTab === "chat" ? "bg-purple-600" : "bg-gray-600 hover:bg-gray-700"
            }`}
          >
            <FiMessageSquare />
            <span>Chat</span>
          </button>
          <button
            onClick={toggleDarkMode}
            className="p-2 hover:bg-gray-700 rounded"
          >
            {darkMode ? <FiSun /> : <FiMoon />}
          </button>
          <button
            onClick={handleExit}
            className="p-2 hover:bg-red-600 rounded text-red-400"
          >
            <FiX />
          </button>
        </div>
      </nav>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* File Explorer */}
        {isFileExplorerOpen && (
          <div className="w-64 bg-gray-200 dark:bg-gray-800 border-r border-gray-300 dark:border-gray-700 flex flex-col">
            <div className="bg-gray-300 dark:bg-gray-700 px-4 py-2 border-b border-gray-400 dark:border-gray-600 flex items-center justify-between">
              <span className="font-semibold text-sm">Explorer</span>
              <button
                onClick={() => setIsFileExplorerOpen(false)}
                className="p-1 hover:bg-gray-400 dark:hover:bg-gray-600 rounded"
              >
                <FiX className="text-xs" />
              </button>
            </div>
            <div className="flex-1 overflow-auto p-2">
              {renderFileTree(fileStructure)}
            </div>
          </div>
        )}

        {/* Toggle File Explorer Button */}
        {!isFileExplorerOpen && (
          <button
            onClick={() => setIsFileExplorerOpen(true)}
            className="w-8 bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 border-r border-gray-400 dark:border-gray-600 flex items-center justify-center"
          >
            <FiFolder className="text-sm" />
          </button>
        )}

        {/* Editor Pane */}
        <div className={`${activeTab === "editor" ? "flex-1" : "flex-1"} overflow-hidden flex flex-col`}>
          <div className="bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-700 px-4 py-1 flex">
            <div className="px-3 py-1 bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 rounded-t border-t border-l border-r border-gray-300 dark:border-gray-600">
              {fileName}
            </div>
          </div>
          <div className="flex-1">
            <Editor
              height="100%"
              language={language}
              value={code}
              onChange={(value) => setCode(value || "")}
              theme={darkMode ? "vs-dark" : "light"}
              options={{
                fontSize: 14,
                fontFamily: 'JetBrains Mono, monospace',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                insertSpaces: true,
                wordWrap: 'on',
              }}
            />
          </div>
        </div>

        {/* Terminal or Chat Pane */}
        {activeTab !== "editor" && (
          <div className="w-80 border-l border-gray-300 dark:border-gray-700 flex flex-col">
            {activeTab === "terminal" ? (
              <>
                <div className="bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-700 px-4 py-2 flex items-center justify-between">
                  <span className="font-semibold text-sm">Terminal</span>
                  <button
                    onClick={() => setActiveTab("editor")}
                    className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded"
                  >
                    <FiX className="text-xs" />
                  </button>
                </div>
                <div ref={terminalRef} className="flex-1 bg-black" />
              </>
            ) : (
              <>
                <div className="bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-700 px-4 py-2 flex items-center justify-between">
                  <span className="font-semibold text-sm">Chat</span>
                  <button
                    onClick={() => setActiveTab("editor")}
                    className="p-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded"
                  >
                    <FiX className="text-xs" />
                  </button>
                </div>
                <div className="flex-1 overflow-auto p-4 bg-gray-50 dark:bg-gray-800 text-sm">
                  <div className="text-center text-gray-500 dark:text-gray-400 mt-8">
                    <FiMessageSquare className="mx-auto text-3xl mb-2" />
                    <p>Chat feature coming soon!</p>
                    <p className="text-xs mt-1">Connect with your team in real-time</p>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
