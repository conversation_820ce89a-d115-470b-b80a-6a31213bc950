
import { Link } from 'react-router-dom';
import { FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';

const Home = () => {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: FaCode,
      title: 'Advanced Code Editor',
      description: 'Powerful Monaco editor with syntax highlighting, auto-completion, and more.',
      color: 'text-green-400',
    },
    {
      icon: FaRocket,
      title: 'Fast Performance',
      description: 'Built with modern technologies for lightning-fast development experience.',
      color: 'text-blue-400',
    },
    {
      icon: FaUsers,
      title: 'Collaborative',
      description: 'Work together with your team in real-time on the same codebase.',
      color: 'text-purple-400',
    },
    {
      icon: FaLightbulb,
      title: 'Smart Features',
      description: 'Intelligent code suggestions and error detection to boost productivity.',
      color: 'text-yellow-400',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-gray-200 font-mono">
      {/* Hero Section */}
      <div className="bg-gray-800 border-b border-green-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-green-400">
              // Welcome to <span className="text-yellow-400">CodeForge</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-400 max-w-3xl mx-auto">
              A modern, powerful code editor and development environment built for developers who demand excellence.
            </p>
            <div className="space-x-4">
              {isAuthenticated ? (
                <Link
                  to="/editor"
                  className="bg-green-500 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-green-400 transition-colors duration-200 inline-block"
                >
                  Start Coding
                </Link>
              ) : (
                <Link
                  to="/login"
                  className="bg-green-500 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-green-400 transition-colors duration-200 inline-block"
                >
                  Get Started
                </Link>
              )}
              <Link
                to="/profile"
                className="border-2 border-gray-400 text-gray-400 px-8 py-3 rounded-lg font-semibold hover:bg-gray-400 hover:text-gray-900 transition-colors duration-200 inline-block"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-green-400 mb-4">
              /* Why Choose CodeForge? */
            </h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Discover the features that make CodeForge the perfect choice for your development needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="bg-gray-800 p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200 border border-gray-700"
                >
                  <div className={`${feature.color} mb-4`}>
                    <Icon className="text-3xl" />
                  </div>
                  <h3 className={`text-xl font-semibold mb-2 ${feature.color}`}>
                    {feature.title}
                  </h3>
                  <p className="text-gray-400">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-800 text-gray-200 py-16 border-t border-green-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-green-400 mb-4">
            /* Ready to Start Building? */
          </h2>
          <p className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
            Join thousands of developers who trust CodeForge for their daily coding needs.
          </p>
          <Link
            to="/editor"
            className="bg-purple-500 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-purple-400 transition-colors duration-200 inline-block"
          >
            Open Editor
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;