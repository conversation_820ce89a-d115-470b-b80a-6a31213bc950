import json
import asyncio
import docker
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from rest_framework_simplejwt.tokens import UntypedToken, AccessToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.contrib.auth import get_user_model
from .models import UserContainer

User = get_user_model()

class TerminalConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # Accept connection first, then handle authentication
        await self.accept()

        # Initialize variables
        self.user = None
        self.container = None
        self.authenticated = False
        self.current_directory = '/home/<USER>'

        # Send welcome message
        await self.send(text_data=json.dumps({
            'output': 'CodeForge Terminal v1.0\r\nSend your JWT token to authenticate.\r\n'
        }))

    @database_sync_to_async
    def get_user_by_token(self, token):
        try:
            # Validate and decode JWT token
            access_token = AccessToken(token)
            user_id = access_token['user_id']
            return User.objects.get(id=user_id)
        except (Invalid<PERSON><PERSON>, TokenError, User.DoesNotExist):
            return None

    @database_sync_to_async
    def get_user_container(self):
        if self.user:
            return UserContainer.objects.filter(user=self.user).first()
        return None

    @database_sync_to_async
    def save_container_record(self, container_record):
        container_record.save()

    async def authenticate_user(self, token):
        """Authenticate user with JWT token"""
        self.user = await self.get_user_by_token(token)
        if self.user:
            self.authenticated = True
            container_record = await self.get_user_container()
            if container_record:
                await self.setup_container(container_record)
                await self.send(text_data=json.dumps({
                    'output': f'\r\n✅ Authenticated as {self.user.username}\r\n'
                }))
                await self.send_welcome_message()
                return True

        await self.send(text_data=json.dumps({
            'output': '\r\n❌ Authentication failed. Invalid token.\r\n'
        }))
        return False

    async def send_welcome_message(self):
        """Send welcome message with file listing"""
        welcome_msg = f"""
╔══════════════════════════════════════════════════════════════╗
║                    CodeForge Terminal                        ║
║                  Welcome {self.user.username:<20}                  ║
╚══════════════════════════════════════════════════════════════╝

Your isolated development environment is ready!
Type 'help' for available commands, 'ls' to see files, 'code <file>' to edit.

"""
        await self.send(text_data=json.dumps({'output': welcome_msg}))

        # Show current directory and files
        await self.execute_command('pwd && echo "Files in your workspace:" && ls -la')

    async def setup_container(self, container_record):
        """Setup Docker container in a thread-safe way"""
        loop = asyncio.get_event_loop()

        def _setup_docker():
            client = docker.from_env()
            try:
                container = client.containers.get(container_record.container_id)
                if container.status != 'running':
                    container.start()
                return container
            except docker.errors.NotFound:
                # Recreate container if it was deleted
                volume_name = container_record.volume_name
                container = client.containers.run(
                    'codeforge_ubuntu:latest',
                    command='tail -f /dev/null',
                    detach=True,
                    volumes={volume_name: {'bind': '/home/<USER>', 'mode': 'rw'}},
                    name=f'codeforge_user_{self.user.id}',
                    remove=False
                )
                container_record.container_id = container.id
                return container

        self.container = await loop.run_in_executor(None, _setup_docker)
        if hasattr(container_record, 'container_id'):
            await self.save_container_record(container_record)

    async def disconnect(self, close_code):
        # Keep container running for persistence
        pass

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)

            # Handle authentication
            if 'type' in data and data['type'] == 'auth':
                token = data.get('token', '')
                await self.authenticate_user(token)
                return

            # Handle terminal input
            if not self.authenticated:
                # Try to authenticate with the input as token
                await self.authenticate_user(text_data.strip())
                return

            command = data.get('input', '')
            if command:
                # Handle special commands
                if command.strip() == 'help':
                    await self.send_help()
                elif command.strip().startswith('code '):
                    filename = command.strip()[5:]
                    await self.open_file_editor(filename)
                elif command.strip() == 'clear':
                    await self.send(text_data=json.dumps({'output': '\033[2J\033[H'}))
                else:
                    # Execute command in container
                    output = await self.execute_command(command)
                    await self.send(text_data=json.dumps({'output': output}))

        except Exception as e:
            await self.send(text_data=json.dumps({
                'output': f'Error: {str(e)}\r\n$ '
            }))

    async def send_help(self):
        """Send help message"""
        help_msg = """
Available Commands:
  help          - Show this help message
  ls            - List files and directories
  pwd           - Show current directory
  cd <dir>      - Change directory
  cat <file>    - Display file contents
  code <file>   - Open file in editor (creates if doesn't exist)
  mkdir <dir>   - Create directory
  touch <file>  - Create empty file
  rm <file>     - Remove file
  clear         - Clear terminal

File Operations:
  nano <file>   - Edit file with nano
  vim <file>    - Edit file with vim

Programming:
  python <file> - Run Python script
  node <file>   - Run JavaScript with Node.js
  gcc <file>    - Compile C program

$ """
        await self.send(text_data=json.dumps({'output': help_msg}))

    async def open_file_editor(self, filename):
        """Open file in a simple editor"""
        if not filename:
            await self.send(text_data=json.dumps({
                'output': 'Usage: code <filename>\r\n$ '
            }))
            return

        # Check if file exists, create if not
        check_cmd = f'test -f {filename} && echo "exists" || touch {filename}'
        await self.execute_command(check_cmd)

        # Show file contents
        content_output = await self.execute_command(f'cat {filename}')

        editor_msg = f"""
╔══════════════════════════════════════════════════════════════╗
║                    File: {filename:<40}           ║
╚══════════════════════════════════════════════════════════════╝

{content_output}

Use 'nano {filename}' or 'vim {filename}' to edit this file.
$ """
        await self.send(text_data=json.dumps({'output': editor_msg}))

    async def execute_command(self, command):
        """Execute command in container asynchronously"""
        if not self.container:
            return 'Error: Container not available\r\n$ '

        loop = asyncio.get_event_loop()

        def _exec_command():
            try:
                # Enhanced command execution with better formatting
                result = self.container.exec_run(
                    cmd=f'cd {self.current_directory} && {command}',
                    tty=True,
                    environment={
                        'TERM': 'xterm-256color',
                        'PS1': '\\u@codeforge:\\w$ ',
                        'HOME': '/home/<USER>'
                    },
                    workdir=self.current_directory
                )
                output = result.output.decode('utf-8', errors='ignore')

                # Update current directory if cd command was used
                if command.strip().startswith('cd '):
                    try:
                        pwd_result = self.container.exec_run('pwd', workdir=self.current_directory)
                        self.current_directory = pwd_result.output.decode('utf-8').strip()
                    except:
                        pass

                # Format output nicely
                if output.strip():
                    return f'{output}\r\n$ '
                else:
                    return '$ '

            except Exception as e:
                return f'Command failed: {str(e)}\r\n$ '

        return await loop.run_in_executor(None, _exec_command)